import 'package:base_app/core/extension/app_extension_num.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:flutter/material.dart';

class DialogUtil {
  static Future<void> showNoInternetDialog(BuildContext context) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Column(
            children: [
              Icon(
                Icons.wifi_off_rounded,
                color: Colors.red,
                size: 56,
              ),
              16.spacingHeight,
              Text(
                'Không có kết nối Internet',
                style: AppTextStyle.bold(18),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: Text(
            'Vui lòng kiểm tra kết nối Internet của bạn và thử lại.',
            textAlign: TextAlign.center,
            style: AppTextStyle.regular(14),
          ),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: [
            AppButton(
              onSubmit: () {
                Navigator.of(dialogContext).pop();
              },
              title: 'Đóng',
            ),
          ],
        );
      },
    );
  }
}
