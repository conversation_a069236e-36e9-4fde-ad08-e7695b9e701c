import 'package:base_app/core/helper/app_hepler.dart';
import 'package:base_app/core/utils/dialog_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class NetworkConnectivityInterceptor extends Interceptor {
  final GlobalKey<NavigatorState> navigatorKey;

  bool _isDialogShowing = false;

  NetworkConnectivityInterceptor({
    required this.navigatorKey,
  });

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final isConnected = await AppHelper.instance.checkInternetConnection();

    if (!isConnected) {
      if (!_isDialogShowing) {
        _showNoInternetDialog();
      }

      handler.reject(
        DioException(
          requestOptions: options,
          type: DioExceptionType.connectionError,
          message: 'No internet connection',
        ),
      );
      return;
    }

    handler.next(options);
  }

  void _showNoInternetDialog() {
    _isDialogShowing = true;

    final context = navigatorKey.currentContext;
    if (context == null) return;

    DialogUtil.showNoInternetDialog(context).then((_) {
      _isDialogShowing = false;
    });
  }
}
