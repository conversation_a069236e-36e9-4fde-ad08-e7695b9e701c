import 'dart:async';
import 'dart:io';

import 'package:base_app/core/constants/app_constants.dart';

class AppHelper {
  AppHelper._();

  static final AppHelper instance = AppHelper._();

  Future<bool> checkInternetConnection() async {
    try {
      final result =
          await InternetAddress.lookup(AppConstants.baseGoogle).timeout(
        const Duration(
          seconds: 5,
        ),
      );
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    } on TimeoutException catch (_) {
      return false;
    } catch (_) {
      return false;
    }
  }
}
