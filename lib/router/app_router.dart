import 'package:base_app/core/utils/navigation_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_error.dart';
import 'package:base_app/features/login/page/login_page.dart';
import 'package:base_app/features/otp/otp_screen.dart';
import 'package:base_app/features/splash/splash_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Global navigator key - export để dùng trong DioClient
final GlobalKey<NavigatorState> rootNavigatorKey = GlobalKey<NavigatorState>();

class AppRouter {
  static const String splash = '/';
  static const String login = '/login';
  static const String otp = '/otp';
  static const String error = '/error';

  static final GoRouter router = GoRouter(
    // QUAN TRỌNG: Thêm navigatorKey vào GoRouter
    navigatorKey: rootNavigatorKey,
    initialLocation: splash,
    routes: [
      GoRoute(
        path: splash,
        builder: (context, state) => const SplashPage(),
      ),
      GoRoute(
        path: login,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const LoginPage(),
          );
        },
      ),
      GoRoute(
        path: otp,
        pageBuilder: (context, state) {
          final args = state.extra as Map<String, dynamic>?;
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: OTPScreen(
              verificationId: args?['verificationId'] ?? '',
              resendToken: args?['resendToken'],
            ),
          );
        },
      ),
      GoRoute(
        path: error,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const AppError(),
          );
        },
      ),
    ],
    errorBuilder: (context, state) => AppBase(
      body: Center(
        child: Text(
          'Page not found: ${state.uri}',
        ),
      ),
    ),
  );
}
